graph TD
    A[Inicio] --> B{Desarrollar Vistas/Endpoints};
    B --> C{Elección de Tecnología};
    C --> C1[Vistas de Django];
    C --> C2[Django REST Framework (DRF)];
    
    C2 --> D[Configurar DRF];
    D --> D1[Crear Serializers];
    D1 --> D2[Crear ViewSets/Vistas de API];
    D2 --> D3[Configurar URLs en urls.py];
    
    B --> E{Crear Endpoints};
    E --> F[/categorias/];
    E --> G[/proveedores/];
    E --> H[/bodegas/];
    E --> I[/productos/];
    E --> J[/movimientos/];
    
    J --> K{Validar Lógica de Negocio};
    K --> L[Movimientos de Salida];
    K --> M[Movimientos de Merma];
    L --> N[Validar stock_actual >= cantidad];
    M --> N;
    
    N --> O[Retornar error si stock insuficiente];
    O --> P[Fin];
