graph TD
    A[Inicio] --> B{Configurar Django};
    B --> C[Editar settings.py];
    C --> D[Definir DATABASES];
    D --> E[Instalar psycopg2-binary];
    E --> F[Conectar a PostgreSQL];
    F --> G{Crear base de datos};
    G --> H[db_user: admin];
    H --> I[db_password: admin1234];
    I --> J[Ejecutar makemigrations];
    J --> K[Ejecutar migrate];
    K --> L[Verificar tablas creadas];
    L --> M[Tomar captura de pantalla o dbshell];
    M --> N[Fin];
