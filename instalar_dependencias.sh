#!/bin/bash

echo "🔧 Configurando variables de entorno para psycopg2..."

# Configurar variables de entorno para libpq
export LDFLAGS="-L/opt/homebrew/opt/libpq/lib"
export CPPFLAGS="-I/opt/homebrew/opt/libpq/include"
export PATH="/opt/homebrew/opt/libpq/bin:$PATH"

echo "📦 Instalando dependencias de Python..."

# Actualizar pip
pip install --upgrade pip

# Instalar psycopg2-binary con las variables configuradas
pip install psycopg2-binary

# Instalar el resto de dependencias
pip install -r requirements.txt

echo "✅ Instalación completada!"
echo "🚀 Ahora puedes ejecutar: python manage.py runserver"
