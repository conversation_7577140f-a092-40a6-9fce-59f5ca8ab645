graph TD
    A[Escala de Apreciación] --> B{Criterios de Evaluación};
    B --> C(1.1.1 Variables y operaciones);
    B --> D(1.1.2 Estructuras y operadores);
    B --> E(1.1.3 Paquetes externos);
    B --> F(1.1.4 Aplicación en Django);
    B --> G(Presentación y entrega);

    subgraph Logrado (7 pts)
        C_L[Define correctamente variables y operaciones]
        D_L[Aplica estructuras de decisión y operadores]
        E_L[Utiliza correctamente paquetes de Django]
        F_L[Implementa CRUD completo y admin configurado]
        G_L[Entrega README claro y archivos organizados]
    end

    subgraph En proceso (5 pts)
        C_EP[Define parcialmente variables u operaciones]
        D_EP[Aplica estructuras de forma incompleta]
        E_EP[Utiliza parcialmente los paquetes]
        F_EP[Implementa CRUD incompleto o admin sin personalización]
        G_EP[Entrega incompleta con README incompleto]
    end

    subgraph No logrado (0-1 pts)
        C_NL[No define variables ni operaciones]
        D_NL[No aplica estructuras ni operadores]
        E_NL[No utiliza paquetes externos o el uso es incorrecto]
        F_NL[No implementa correctamente la aplicación]
        G_NL[Entrega desordenada o sin README]
    end

    C --> C_L;
    C --> C_EP;
    C --> C_NL;

    D --> D_L;
    D --> D_EP;
    D --> D_NL;

    E --> E_L;
    E --> E_EP;
    E --> E_NL;

    F --> F_L;
    F --> F_EP;
    F --> F_NL;

    G --> G_L;
    G --> G_EP;
    G --> G_NL;
