#!/usr/bin/env python3
"""
Script para ver el histórico de movimientos de un producto específico
"""

import os
import sys
import django

# Configurar Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventario_project.settings')
django.setup()

from inventario.models import Producto, Movimiento

def mostrar_historico_por_id(producto_id):
    """Mostrar histórico por ID de producto"""
    try:
        producto = Producto.objects.get(id=producto_id)
        mostrar_historico_producto(producto)
    except Producto.DoesNotExist:
        print(f"❌ No se encontró producto con ID: {producto_id}")

def mostrar_historico_por_sku(sku):
    """Mostrar histórico por SKU de producto"""
    try:
        producto = Producto.objects.get(sku=sku)
        mostrar_historico_producto(producto)
    except Producto.DoesNotExist:
        print(f"❌ No se encontró producto con SKU: {sku}")

def mostrar_historico_producto(producto):
    """Mostrar histórico completo de un producto"""
    print("=" * 70)
    print(f"📦 HISTÓRICO DE MOVIMIENTOS")
    print("=" * 70)
    print(f"Producto: {producto.nombre}")
    print(f"SKU: {producto.sku}")
    print(f"Stock Actual: {producto.stock_actual} unidades")
    print(f"Categoría: {producto.categoria.nombre}")
    print(f"Proveedor: {producto.proveedor.razon_social}")
    print("-" * 70)
    
    # Obtener movimientos ordenados por fecha (más reciente primero)
    movimientos = Movimiento.objects.filter(producto=producto).select_related('bodega').order_by('-fecha')
    
    if not movimientos.exists():
        print("📭 No hay movimientos registrados para este producto")
        return
    
    print(f"📊 Total de movimientos: {movimientos.count()}")
    print("-" * 70)
    
    # Mostrar cada movimiento
    for i, mov in enumerate(movimientos, 1):
        tipo_emoji = {
            'entrada': '⬆️ ',
            'salida': '⬇️ ',
            'merma': '❌'
        }.get(mov.tipo, '❓')
        
        print(f"{i:2d}. {tipo_emoji} {mov.tipo.upper()}")
        print(f"     Fecha: {mov.fecha.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"     Cantidad: {mov.cantidad} unidades")
        print(f"     Bodega: {mov.bodega.nombre}")
        if mov.observacion:
            print(f"     Observación: {mov.observacion}")
        print()
    
    # Calcular resumen
    entradas = movimientos.filter(tipo='entrada')
    salidas = movimientos.filter(tipo='salida')
    mermas = movimientos.filter(tipo='merma')
    
    total_entradas = sum(m.cantidad for m in entradas)
    total_salidas = sum(m.cantidad for m in salidas)
    total_mermas = sum(m.cantidad for m in mermas)
    
    print("-" * 70)
    print("📈 RESUMEN DE MOVIMIENTOS:")
    print(f"   ⬆️  Entradas: {entradas.count()} movimientos, {total_entradas} unidades")
    print(f"   ⬇️  Salidas: {salidas.count()} movimientos, {total_salidas} unidades")
    print(f"   ❌ Mermas: {mermas.count()} movimientos, {total_mermas} unidades")
    print(f"   📊 Balance: {total_entradas - total_salidas - total_mermas} unidades")
    print("=" * 70)

def listar_productos():
    """Listar todos los productos disponibles"""
    print("📦 PRODUCTOS DISPONIBLES:")
    print("-" * 50)
    productos = Producto.objects.all().order_by('sku')
    
    for producto in productos:
        movimientos_count = producto.movimiento_set.count()
        print(f"  ID: {producto.id:2d} | SKU: {producto.sku:12s} | {producto.nombre}")
        print(f"       Stock: {producto.stock_actual:3d} | Movimientos: {movimientos_count}")
        print()

def main():
    if len(sys.argv) < 2:
        print("🔍 VISUALIZADOR DE HISTÓRICO DE PRODUCTOS")
        print("=" * 50)
        print("Uso:")
        print("  python historico_producto.py <ID>     # Por ID de producto")
        print("  python historico_producto.py <SKU>    # Por SKU de producto")
        print("  python historico_producto.py list     # Listar productos")
        print()
        print("Ejemplos:")
        print("  python historico_producto.py 1")
        print("  python historico_producto.py LAPTOP001")
        print("  python historico_producto.py list")
        return
    
    parametro = sys.argv[1]
    
    if parametro.lower() == 'list':
        listar_productos()
    elif parametro.isdigit():
        mostrar_historico_por_id(int(parametro))
    else:
        mostrar_historico_por_sku(parametro)

if __name__ == "__main__":
    main()
