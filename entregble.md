graph TD
    A[Inicio] --> B{Entregables del Proyecto};
    
    B --> C[1. Código del Proyecto];
    C --> C1[Proyecto Django completo];
    C1 --> C2[Modelos, Vistas, URLs, etc.];

    B --> D[2. Evidencia de Pruebas];
    D --> D1[Capturas de Pantalla];
    D1 --> D2[Estado de las tablas en la BD];
    D2 --> D3[CRUD de modelos];
    D3 --> D4[Validación de stock];
    
    B --> E[3. Documentación];
    E --> E1[Documento (2-3 páginas)];
    E1 --> E1a[Diseño de Modelos y Relaciones];
    E1 --> E1b[Lógica de Movimientos];
    E1 --> E1c[Pruebas y Resultados];
    
    B --> F[4. Instrucciones de Uso];
    F --> F1[Archivo README.md];
    F1 --> F2[Instalación de dependencias];
    F1 --> F3[Configuración de la BD];
    F1 --> F4[Ejecución de la aplicación];

    F --> G[Fin];
