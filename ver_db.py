#!/usr/bin/env python3
"""
Script para ver el contenido de la base de datos de forma amigable
"""

import os
import sys
import django

# Configurar Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventario_project.settings')
django.setup()

from inventario.models import Categoria, Proveedor, Bodega, Producto, Movimiento

def mostrar_categorias():
    print("📂 CATEGORÍAS:")
    print("-" * 50)
    for cat in Categoria.objects.all():
        productos_count = cat.producto_set.count()
        print(f"  • {cat.nombre} ({productos_count} productos)")
        if cat.descripcion:
            print(f"    {cat.descripcion}")
    print()

def mostrar_proveedores():
    print("🏢 PROVEEDORES:")
    print("-" * 50)
    for prov in Proveedor.objects.all():
        productos_count = prov.producto_set.count()
        print(f"  • {prov.razon_social}")
        print(f"    RUT: {prov.rut} | Email: {prov.email}")
        print(f"    Teléfono: {prov.telefono} | Productos: {productos_count}")
    print()

def mostrar_bodegas():
    print("🏪 BODEGAS:")
    print("-" * 50)
    for bodega in Bodega.objects.all():
        movimientos_count = bodega.movimiento_set.count()
        print(f"  • {bodega.nombre}")
        print(f"    Ubicación: {bodega.ubicacion}")
        print(f"    Movimientos: {movimientos_count}")
    print()

def mostrar_productos():
    print("📦 PRODUCTOS:")
    print("-" * 50)
    for prod in Producto.objects.select_related('categoria', 'proveedor').all():
        stock_status = "🔴 BAJO" if prod.stock_actual < 10 else "🟢 OK"
        print(f"  • {prod.sku} - {prod.nombre}")
        print(f"    Categoría: {prod.categoria.nombre}")
        print(f"    Proveedor: {prod.proveedor.razon_social}")
        print(f"    Precio: ${prod.precio:,.2f}")
        print(f"    Stock: {prod.stock_actual} {stock_status}")
        print()

def mostrar_movimientos():
    print("📊 MOVIMIENTOS (Últimos 10):")
    print("-" * 50)
    movimientos = Movimiento.objects.select_related('producto', 'bodega').order_by('-fecha')[:10]
    
    for mov in movimientos:
        tipo_emoji = {"entrada": "⬆️", "salida": "⬇️", "merma": "❌"}.get(mov.tipo, "❓")
        print(f"  {tipo_emoji} {mov.tipo.upper()}")
        print(f"    Producto: {mov.producto.nombre}")
        print(f"    Cantidad: {mov.cantidad}")
        print(f"    Bodega: {mov.bodega.nombre}")
        print(f"    Fecha: {mov.fecha.strftime('%Y-%m-%d %H:%M')}")
        if mov.observacion:
            print(f"    Observación: {mov.observacion}")
        print()

def mostrar_resumen():
    print("📈 RESUMEN GENERAL:")
    print("-" * 50)
    print(f"  📂 Categorías: {Categoria.objects.count()}")
    print(f"  🏢 Proveedores: {Proveedor.objects.count()}")
    print(f"  🏪 Bodegas: {Bodega.objects.count()}")
    print(f"  📦 Productos: {Producto.objects.count()}")
    print(f"  📊 Movimientos: {Movimiento.objects.count()}")
    
    # Stock total
    total_stock = sum(p.stock_actual for p in Producto.objects.all())
    print(f"  📈 Stock Total: {total_stock} unidades")
    
    # Productos con stock bajo
    bajo_stock = Producto.objects.filter(stock_actual__lt=10).count()
    print(f"  🔴 Productos con stock bajo: {bajo_stock}")
    print()

def main():
    print("🗄️  VISUALIZADOR DE BASE DE DATOS - SISTEMA INVENTARIO")
    print("=" * 70)
    print()
    
    try:
        mostrar_resumen()
        mostrar_categorias()
        mostrar_proveedores()
        mostrar_bodegas()
        mostrar_productos()
        mostrar_movimientos()
        
        print("✅ Visualización completada!")
        print("💡 Para ver más detalles, usa el admin: http://localhost:8000/admin/")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("💡 Asegúrate de que la base de datos esté configurada y el servidor Django funcione")

if __name__ == "__main__":
    main()
