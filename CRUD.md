graph TD
    A[Inicio] --> B{CRUD para 2 modelos principales};
    B --> C[Implementar Producto];
    B --> D[Implementar Proveedor];
    C --> C1{Crear};
    C1 --> C1a[Formulario o Endpoint POST];
    C --> C2{<PERSON><PERSON>};
    C2 --> C2a[Listar todos];
    C2 --> C2b[Ver detalle por ID];
    C --> C3{Actualizar};
    C3 --> C3a[Formulario o Endpoint PUT/PATCH];
    C --> C4{Eliminar};
    C4 --> C4a[Baja lógica o real];
    D --> D1[Repetir lógica CRUD];
    
    A --> E{Implementar Movimientos};
    E --> F[Crear modelo Movimiento];
    F --> G[Crear vista/lógica de negocio];
    G --> G1{Tipo Entrada};
    G1 --> G1a[Sumar a stock_actual];
    G --> G2{Tipo Salida};
    G2 --> G2a[Restar a stock_actual];
    G2 --> G2b[Validar stock >= cantidad];
    G --> G3{Tipo Merma};
    G3 --> G3a[Restar a stock_actual];
    G3 --> G3b[Validar stock >= cantidad];
    
    G --> H[Crear vista/endpoint];
    H --> I[Mostrar histórico de movimientos];
    I --> J[Filtrar por producto];
    I --> K[Fin];
