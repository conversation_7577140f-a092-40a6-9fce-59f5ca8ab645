#!/usr/bin/env python3
"""
Script de diagnóstico para problemas con el servidor Django
"""

import os
import sys
import subprocess
import socket

def verificar_python():
    """Verificar versión de Python"""
    print("🐍 Verificando Python...")
    print(f"   Versión: {sys.version}")
    print(f"   Ejecutable: {sys.executable}")
    
    # Verificar que es Python 3.8+
    if sys.version_info < (3, 8):
        print("   ❌ Se requiere Python 3.8 o superior")
        return False
    else:
        print("   ✅ Versión de Python compatible")
        return True

def verificar_django():
    """Verificar instalación de Django"""
    print("\n🎯 Verificando Django...")
    try:
        import django
        print(f"   ✅ Django instalado: {django.get_version()}")
        return True
    except ImportError:
        print("   ❌ Django no está instalado")
        print("   💡 Ejecuta: pip install Django==4.2.7")
        return False

def verificar_dependencias():
    """Verificar dependencias del proyecto"""
    print("\n📦 Verificando dependencias...")
    dependencias = [
        ('psycopg2', 'psycopg2'),
        ('rest_framework', 'djangorestframework'),
        ('decouple', 'python-decouple')
    ]
    
    todas_ok = True
    for modulo, paquete in dependencias:
        try:
            __import__(modulo)
            print(f"   ✅ {paquete}")
        except ImportError:
            print(f"   ❌ {paquete} no instalado")
            todas_ok = False
    
    if not todas_ok:
        print("   💡 Ejecuta: pip install -r requirements.txt")
    
    return todas_ok

def verificar_archivos():
    """Verificar archivos del proyecto"""
    print("\n📁 Verificando archivos del proyecto...")
    archivos_requeridos = [
        'manage.py',
        'inventario_project/settings.py',
        'inventario/models.py',
        'requirements.txt'
    ]
    
    todos_ok = True
    for archivo in archivos_requeridos:
        if os.path.exists(archivo):
            print(f"   ✅ {archivo}")
        else:
            print(f"   ❌ {archivo} no encontrado")
            todos_ok = False
    
    return todos_ok

def verificar_postgresql():
    """Verificar PostgreSQL"""
    print("\n🐘 Verificando PostgreSQL...")
    
    # Verificar si psql está disponible
    try:
        result = subprocess.run(['which', 'psql'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"   ✅ psql encontrado en: {result.stdout.strip()}")
        else:
            print("   ❌ psql no encontrado")
            return False
    except:
        print("   ❌ Error verificando psql")
        return False
    
    # Verificar si el servicio está corriendo
    try:
        result = subprocess.run(['brew', 'services', 'list'], capture_output=True, text=True)
        if 'postgresql' in result.stdout and 'started' in result.stdout:
            print("   ✅ PostgreSQL está ejecutándose")
        else:
            print("   ❌ PostgreSQL no está ejecutándose")
            print("   💡 Ejecuta: brew services start postgresql")
            return False
    except:
        print("   ⚠️  No se pudo verificar el estado del servicio")
    
    return True

def verificar_puerto():
    """Verificar si el puerto 8000 está disponible"""
    print("\n🔌 Verificando puerto 8000...")
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    try:
        result = sock.connect_ex(('127.0.0.1', 8000))
        if result == 0:
            print("   ⚠️  Puerto 8000 está ocupado")
            print("   💡 Usa otro puerto: python manage.py runserver 8001")
            return False
        else:
            print("   ✅ Puerto 8000 disponible")
            return True
    finally:
        sock.close()

def verificar_migraciones():
    """Verificar estado de migraciones"""
    print("\n🗄️  Verificando migraciones...")
    try:
        result = subprocess.run([
            sys.executable, 'manage.py', 'showmigrations', '--plan'
        ], capture_output=True, text=True, cwd='.')
        
        if result.returncode == 0:
            if '[X]' in result.stdout:
                print("   ✅ Migraciones aplicadas")
                return True
            else:
                print("   ❌ Migraciones pendientes")
                print("   💡 Ejecuta: python manage.py migrate")
                return False
        else:
            print(f"   ❌ Error verificando migraciones: {result.stderr}")
            return False
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def main():
    print("🔍 DIAGNÓSTICO DEL SISTEMA DJANGO")
    print("=" * 50)
    
    verificaciones = [
        verificar_python(),
        verificar_django(),
        verificar_dependencias(),
        verificar_archivos(),
        verificar_postgresql(),
        verificar_puerto(),
        verificar_migraciones()
    ]
    
    print("\n" + "=" * 50)
    if all(verificaciones):
        print("✅ TODAS LAS VERIFICACIONES PASARON")
        print("🚀 El servidor debería funcionar correctamente")
        print("\n💡 Intenta ejecutar:")
        print("   python3 manage.py runserver")
    else:
        print("❌ ALGUNAS VERIFICACIONES FALLARON")
        print("🔧 Revisa los errores arriba y sigue las sugerencias")
    
    print("\n📞 Si el problema persiste, comparte este diagnóstico")

if __name__ == "__main__":
    main()
