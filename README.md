# Sistema de Inventario Django

Sistema de gestión de inventario desarrollado con Django y Django REST Framework.

## Características

- **Modelos**: Categorías, Proveedores, Bodegas, Productos y Movimientos
- **CRUD completo** para productos y proveedores
- **Sistema de movimientos** con validación automática de stock
- **API REST** con Django REST Framework
- **Admin personalizado** con filtros y búsquedas
- **Validación de stock** para movimientos de salida y merma

## Estructura del Proyecto

```
inventario/
├── inventario_project/     # Configuración del proyecto
├── inventario/            # Aplicación principal
│   ├── models.py         # Modelos de datos
│   ├── views.py          # Vistas de la API
│   ├── serializers.py    # Serializers para la API
│   ├── admin.py          # Configuración del admin
│   └── management/       # Comandos personalizados
├── requirements.txt      # Dependencias
└── README.md            # Este archivo
```

## Instalación y Configuración

### 1. Instalar dependencias

```bash
pip install -r requirements.txt
```

### 2. Configurar la base de datos

El proyecto está configurado para usar SQLite por defecto. Para usar PostgreSQL:

1. Instalar PostgreSQL y crear la base de datos:
```sql
CREATE DATABASE inventario_db;
CREATE USER admin WITH PASSWORD 'admin1234';
GRANT ALL PRIVILEGES ON DATABASE inventario_db TO admin;
```

2. Descomentar la configuración de PostgreSQL en `settings.py`

### 3. Ejecutar migraciones

```bash
python manage.py makemigrations
python manage.py migrate
```

### 4. Crear superusuario

```bash
python manage.py createsuperuser
```

### 5. Cargar datos de prueba

```bash
python manage.py cargar_datos
```

### 6. Ejecutar el servidor

```bash
python manage.py runserver
```

## Uso del Sistema

### Admin Panel
- Acceder a: `http://localhost:8000/admin/`
- Usuario: `admin`
- Contraseña: `admin123`

### API Endpoints

#### Categorías
- `GET /api/categorias/` - Listar categorías
- `POST /api/categorias/` - Crear categoría
- `GET /api/categorias/{id}/` - Obtener categoría
- `PUT /api/categorias/{id}/` - Actualizar categoría
- `DELETE /api/categorias/{id}/` - Eliminar categoría

#### Proveedores
- `GET /api/proveedores/` - Listar proveedores
- `POST /api/proveedores/` - Crear proveedor
- `GET /api/proveedores/{id}/` - Obtener proveedor
- `PUT /api/proveedores/{id}/` - Actualizar proveedor
- `DELETE /api/proveedores/{id}/` - Eliminar proveedor

#### Productos
- `GET /api/productos/` - Listar productos
- `GET /api/productos/bajo_stock/` - Productos con stock bajo
- `POST /api/productos/` - Crear producto
- `GET /api/productos/{id}/` - Obtener producto
- `PUT /api/productos/{id}/` - Actualizar producto
- `DELETE /api/productos/{id}/` - Eliminar producto

#### Movimientos
- `GET /api/movimientos/` - Listar movimientos
- `GET /api/movimientos/resumen/` - Resumen de movimientos
- `POST /api/movimientos/` - Crear movimiento
- `GET /api/movimientos/{id}/` - Obtener movimiento

### Filtros de API

#### Productos
- `?categoria=1` - Filtrar por categoría
- `?proveedor=1` - Filtrar por proveedor
- `?search=laptop` - Buscar por nombre o SKU

#### Movimientos
- `?producto=1` - Filtrar por producto
- `?tipo=entrada` - Filtrar por tipo de movimiento

## Lógica de Negocio

### Movimientos de Stock

1. **Entrada**: Suma la cantidad al stock actual del producto
2. **Salida**: Resta la cantidad del stock actual (valida que haya stock suficiente)
3. **Merma**: Resta la cantidad del stock actual (valida que haya stock suficiente)

### Validaciones

- Los movimientos de salida y merma validan que haya stock suficiente
- Los SKU de productos deben ser únicos
- Los RUT de proveedores deben ser únicos
- Los nombres de categorías deben ser únicos

## Tecnologías Utilizadas

- **Django 4.2.7**: Framework web
- **Django REST Framework**: API REST
- **PostgreSQL/SQLite**: Base de datos
- **Python 3.9+**: Lenguaje de programación

## Estructura de la Base de Datos

### Modelos y Relaciones

- **Categoria**: Categorías de productos
- **Proveedor**: Proveedores de productos
- **Bodega**: Ubicaciones de almacenamiento
- **Producto**: Productos del inventario (FK a Categoria y Proveedor)
- **Movimiento**: Movimientos de stock (FK a Producto y Bodega)

## Comandos Útiles

```bash
# Crear migraciones
python manage.py makemigrations

# Aplicar migraciones
python manage.py migrate

# Crear superusuario
python manage.py createsuperuser

# Cargar datos de prueba
python manage.py cargar_datos

# Ejecutar servidor
python manage.py runserver

# Shell de Django
python manage.py shell
```

## Desarrollo

Para contribuir al proyecto:

1. Fork el repositorio
2. Crear una rama para la nueva funcionalidad
3. Realizar los cambios
4. Ejecutar las pruebas
5. Crear un Pull Request

## Licencia

Este proyecto es de uso educativo.
