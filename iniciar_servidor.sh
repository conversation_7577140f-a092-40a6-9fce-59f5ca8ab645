#!/bin/bash

echo "🚀 Iniciando servidor D<PERSON>go..."
echo "================================"

# Opción 1: Comando estándar
echo "Intentando: python3 manage.py runserver"
python3 manage.py runserver

# Si falla, intentar con python
echo "Intentando: python manage.py runserver"
python manage.py runserver

# Si falla, intentar con otro puerto
echo "Intentando en puerto 8001: python3 manage.py runserver 8001"
python3 manage.py runserver 8001
