#!/usr/bin/env python3
"""
Script para verificar el estado de la base de datos PostgreSQL
"""

import os
import sys
import django

# Configurar Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventario_project.settings')
django.setup()

from django.db import connection
from inventario.models import Categoria, Proveedor, Bodega, Producto, Movimiento

def verificar_conexion():
    """Verificar conexión a PostgreSQL"""
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT version();")
            version = cursor.fetchone()[0]
            print(f"✅ Conexión exitosa a PostgreSQL")
            print(f"   Versión: {version}")
            return True
    except Exception as e:
        print(f"❌ Error de conexión: {e}")
        return False

def verificar_tablas():
    """Verificar que las tablas existen"""
    try:
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name LIKE 'inventario_%'
                ORDER BY table_name;
            """)
            tablas = cursor.fetchall()
            
            print(f"\n📋 Tablas del inventario encontradas:")
            for tabla in tablas:
                print(f"   - {tabla[0]}")
            
            return len(tablas) > 0
    except Exception as e:
        print(f"❌ Error verificando tablas: {e}")
        return False

def verificar_datos():
    """Verificar datos de prueba"""
    try:
        print(f"\n📊 Datos en la base de datos:")
        print(f"   - Categorías: {Categoria.objects.count()}")
        print(f"   - Proveedores: {Proveedor.objects.count()}")
        print(f"   - Bodegas: {Bodega.objects.count()}")
        print(f"   - Productos: {Producto.objects.count()}")
        print(f"   - Movimientos: {Movimiento.objects.count()}")
        
        # Mostrar productos con stock
        print(f"\n🏷️  Productos con stock:")
        for producto in Producto.objects.all():
            print(f"   - {producto.sku}: {producto.nombre} (Stock: {producto.stock_actual})")
        
        return True
    except Exception as e:
        print(f"❌ Error verificando datos: {e}")
        return False

def main():
    print("🔍 Verificando configuración de PostgreSQL...\n")
    
    # Verificar conexión
    if not verificar_conexion():
        sys.exit(1)
    
    # Verificar tablas
    if not verificar_tablas():
        print("\n⚠️  No se encontraron tablas. Ejecuta: python manage.py migrate")
        sys.exit(1)
    
    # Verificar datos
    if not verificar_datos():
        sys.exit(1)
    
    print(f"\n✅ Verificación completada exitosamente!")
    print(f"🚀 El sistema está listo para usar con PostgreSQL")

if __name__ == "__main__":
    main()
