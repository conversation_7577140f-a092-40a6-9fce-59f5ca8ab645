from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

router = DefaultRouter()
router.register(r'categorias', views.CategoriaViewSet)
router.register(r'proveedores', views.ProveedorViewSet)
router.register(r'bodegas', views.BodegaViewSet)
router.register(r'productos', views.ProductoViewSet)
router.register(r'movimientos', views.MovimientoViewSet)

urlpatterns = [
    path('api/', include(router.urls)),
]
