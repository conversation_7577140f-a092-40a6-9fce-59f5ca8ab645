from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Q
from .models import Categoria, Proveedor, Bodega, Producto, Movimiento
from .serializers import (
    CategoriaSerializer, ProveedorSerializer, BodegaSerializer,
    ProductoSerializer, MovimientoSerializer
)


class CategoriaViewSet(viewsets.ModelViewSet):
    queryset = Categoria.objects.all()
    serializer_class = CategoriaSerializer


class ProveedorViewSet(viewsets.ModelViewSet):
    queryset = Proveedor.objects.all()
    serializer_class = ProveedorSerializer


class BodegaViewSet(viewsets.ModelViewSet):
    queryset = Bodega.objects.all()
    serializer_class = BodegaSerializer


class ProductoViewSet(viewsets.ModelViewSet):
    queryset = Producto.objects.select_related('categoria', 'proveedor').all()
    serializer_class = ProductoSerializer

    def get_queryset(self):
        queryset = super().get_queryset()
        categoria = self.request.query_params.get('categoria')
        proveedor = self.request.query_params.get('proveedor')
        search = self.request.query_params.get('search')

        if categoria:
            queryset = queryset.filter(categoria_id=categoria)
        if proveedor:
            queryset = queryset.filter(proveedor_id=proveedor)
        if search:
            queryset = queryset.filter(
                Q(nombre__icontains=search) | Q(sku__icontains=search)
            )

        return queryset

    @action(detail=False, methods=['get'])
    def bajo_stock(self, request):
        """Productos con stock bajo (menos de 10 unidades)"""
        productos = self.get_queryset().filter(stock_actual__lt=10)
        serializer = self.get_serializer(productos, many=True)
        return Response(serializer.data)


class MovimientoViewSet(viewsets.ModelViewSet):
    queryset = Movimiento.objects.select_related('producto', 'bodega').all()
    serializer_class = MovimientoSerializer

    def get_queryset(self):
        queryset = super().get_queryset()
        producto = self.request.query_params.get('producto')
        tipo = self.request.query_params.get('tipo')

        if producto:
            queryset = queryset.filter(producto_id=producto)
        if tipo:
            queryset = queryset.filter(tipo=tipo)

        return queryset.order_by('-fecha')

    @action(detail=False, methods=['get'])
    def resumen(self, request):
        """Resumen de movimientos por tipo"""
        from django.db.models import Count, Sum

        resumen = Movimiento.objects.values('tipo').annotate(
            total_movimientos=Count('id'),
            total_cantidad=Sum('cantidad')
        )

        return Response(resumen)
