from rest_framework import serializers
from .models import Categoria, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>o, Movimiento


class CategoriaSerializer(serializers.ModelSerializer):
    class Meta:
        model = Categoria
        fields = '__all__'


class ProveedorSerializer(serializers.ModelSerializer):
    class Meta:
        model = Proveedor
        fields = '__all__'


class BodegaSerializer(serializers.ModelSerializer):
    class Meta:
        model = Bodega
        fields = '__all__'


class ProductoSerializer(serializers.ModelSerializer):
    categoria_nombre = serializers.CharField(source='categoria.nombre', read_only=True)
    proveedor_nombre = serializers.CharField(source='proveedor.razon_social', read_only=True)
    
    class Meta:
        model = Producto
        fields = ['id', 'sku', 'nombre', 'categoria', 'categoria_nombre', 
                 'proveedor', 'proveedor_nombre', 'precio', 'stock_actual']
        read_only_fields = ['stock_actual']


class MovimientoSerializer(serializers.ModelSerializer):
    producto_nombre = serializers.Char<PERSON>ield(source='producto.nombre', read_only=True)
    bodega_nombre = serializers.CharField(source='bodega.nombre', read_only=True)
    
    class Meta:
        model = Movimiento
        fields = ['id', 'producto', 'producto_nombre', 'bodega', 'bodega_nombre',
                 'tipo', 'cantidad', 'fecha', 'observacion']
        read_only_fields = ['fecha']

    def validate(self, data):
        if data['tipo'] in ['salida', 'merma']:
            producto = data['producto']
            if data['cantidad'] > producto.stock_actual:
                raise serializers.ValidationError(
                    f'Stock insuficiente. Stock actual: {producto.stock_actual}'
                )
        return data
