from django.core.management.base import BaseCommand
from inventario.models import Categoria, Proveedor, Bodega, Producto, Movimiento


class Command(BaseCommand):
    help = 'Carga datos de prueba en la base de datos'

    def handle(self, *args, **options):
        self.stdout.write('Cargando datos de prueba...')

        # Crear categorías
        categorias = [
            {'nombre': 'Electrónicos', 'descripcion': 'Productos electrónicos y tecnológicos'},
            {'nombre': 'Oficina', 'descripcion': 'Suministros de oficina'},
            {'nombre': 'Limpieza', 'descripcion': 'Productos de limpieza e higiene'},
        ]
        
        for cat_data in categorias:
            categoria, created = Categoria.objects.get_or_create(**cat_data)
            if created:
                self.stdout.write(f'Categoría creada: {categoria.nombre}')

        # Crear proveedores
        proveedores = [
            {
                'razon_social': 'TechSupply S.A.',
                'rut': '12345678-9',
                'email': '<EMAIL>',
                'telefono': '+56912345678'
            },
            {
                'razon_social': 'Oficina Total Ltda.',
                'rut': '87654321-0',
                'email': '<EMAIL>',
                'telefono': '+56987654321'
            },
        ]
        
        for prov_data in proveedores:
            proveedor, created = Proveedor.objects.get_or_create(**prov_data)
            if created:
                self.stdout.write(f'Proveedor creado: {proveedor.razon_social}')

        # Crear bodegas
        bodegas = [
            {'nombre': 'Bodega Central', 'ubicacion': 'Santiago Centro'},
            {'nombre': 'Bodega Norte', 'ubicacion': 'Las Condes'},
        ]
        
        for bod_data in bodegas:
            bodega, created = Bodega.objects.get_or_create(**bod_data)
            if created:
                self.stdout.write(f'Bodega creada: {bodega.nombre}')

        # Crear productos
        productos = [
            {
                'sku': 'LAPTOP001',
                'nombre': 'Laptop Dell Inspiron 15',
                'categoria': Categoria.objects.get(nombre='Electrónicos'),
                'proveedor': Proveedor.objects.get(razon_social='TechSupply S.A.'),
                'precio': 599999.99,
                'stock_actual': 0
            },
            {
                'sku': 'MOUSE001',
                'nombre': 'Mouse Inalámbrico Logitech',
                'categoria': Categoria.objects.get(nombre='Electrónicos'),
                'proveedor': Proveedor.objects.get(razon_social='TechSupply S.A.'),
                'precio': 25990.00,
                'stock_actual': 0
            },
            {
                'sku': 'PAPEL001',
                'nombre': 'Resma Papel A4 500 hojas',
                'categoria': Categoria.objects.get(nombre='Oficina'),
                'proveedor': Proveedor.objects.get(razon_social='Oficina Total Ltda.'),
                'precio': 4990.00,
                'stock_actual': 0
            },
        ]
        
        for prod_data in productos:
            producto, created = Producto.objects.get_or_create(**prod_data)
            if created:
                self.stdout.write(f'Producto creado: {producto.nombre}')

        # Crear movimientos de entrada
        bodega_central = Bodega.objects.get(nombre='Bodega Central')
        
        movimientos = [
            {
                'producto': Producto.objects.get(sku='LAPTOP001'),
                'bodega': bodega_central,
                'tipo': 'entrada',
                'cantidad': 10,
                'observacion': 'Stock inicial'
            },
            {
                'producto': Producto.objects.get(sku='MOUSE001'),
                'bodega': bodega_central,
                'tipo': 'entrada',
                'cantidad': 50,
                'observacion': 'Stock inicial'
            },
            {
                'producto': Producto.objects.get(sku='PAPEL001'),
                'bodega': bodega_central,
                'tipo': 'entrada',
                'cantidad': 100,
                'observacion': 'Stock inicial'
            },
        ]
        
        for mov_data in movimientos:
            movimiento = Movimiento(**mov_data)
            movimiento.save()
            self.stdout.write(f'Movimiento creado: {movimiento}')

        self.stdout.write(self.style.SUCCESS('Datos de prueba cargados exitosamente!'))
