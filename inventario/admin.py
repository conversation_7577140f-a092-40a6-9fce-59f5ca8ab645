from django.contrib import admin
from .models import Categoria, <PERSON><PERSON><PERSON><PERSON>, Bodega, Producto, Movimiento


@admin.register(Categoria)
class CategoriaAdmin(admin.ModelAdmin):
    list_display = ['nombre', 'descripcion']
    search_fields = ['nombre']


@admin.register(Proveedor)
class ProveedorAdmin(admin.ModelAdmin):
    list_display = ['razon_social', 'rut', 'email', 'telefono']
    search_fields = ['razon_social', 'rut']
    list_filter = ['razon_social']


@admin.register(Bodega)
class BodegaAdmin(admin.ModelAdmin):
    list_display = ['nombre', 'ubicacion']
    search_fields = ['nombre', 'ubicacion']


@admin.register(Producto)
class ProductoAdmin(admin.ModelAdmin):
    list_display = ['sku', 'nombre', 'categoria', 'proveedor', 'precio', 'stock_actual']
    search_fields = ['sku', 'nombre']
    list_filter = ['categoria', 'proveedor']
    readonly_fields = ['stock_actual']


@admin.register(Movimiento)
class MovimientoAdmin(admin.ModelAdmin):
    list_display = ['producto', 'tipo', 'cantidad', 'bodega', 'fecha']
    search_fields = ['producto__nombre', 'producto__sku']
    list_filter = ['tipo', 'fecha', 'bodega']
    readonly_fields = ['fecha']
