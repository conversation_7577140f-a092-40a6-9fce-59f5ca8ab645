from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from .models import Categoria, Proveedor, Bodega, Producto, Movimiento


class ModelTestCase(TestCase):
    def setUp(self):
        self.categoria = Categoria.objects.create(
            nombre="Electrónicos",
            descripcion="Productos electrónicos"
        )
        self.proveedor = Proveedor.objects.create(
            razon_social="TechSupply S.A.",
            rut="12345678-9",
            email="<EMAIL>",
            telefono="+56912345678"
        )
        self.bodega = Bodega.objects.create(
            nombre="Bodega Central",
            ubicacion="Santiago Centro"
        )
        self.producto = Producto.objects.create(
            sku="TEST001",
            nombre="Producto de Prueba",
            categoria=self.categoria,
            proveedor=self.proveedor,
            precio=10000.00,
            stock_actual=0
        )

    def test_categoria_str(self):
        self.assertEqual(str(self.categoria), "Electrónicos")

    def test_proveedor_str(self):
        self.assertEqual(str(self.proveedor), "TechSupply S.A.")

    def test_producto_str(self):
        self.assertEqual(str(self.producto), "TEST001 - Producto de Prueba")

    def test_movimiento_entrada(self):
        movimiento = Movimiento.objects.create(
            producto=self.producto,
            bodega=self.bodega,
            tipo='entrada',
            cantidad=10,
            observacion='Entrada inicial'
        )

        # Recargar el producto desde la base de datos
        self.producto.refresh_from_db()
        self.assertEqual(self.producto.stock_actual, 10)
        self.assertEqual(str(movimiento), "Entrada - Producto de Prueba - 10")

    def test_movimiento_salida_con_stock(self):
        # Primero agregar stock
        Movimiento.objects.create(
            producto=self.producto,
            bodega=self.bodega,
            tipo='entrada',
            cantidad=20
        )

        # Luego hacer una salida
        movimiento = Movimiento.objects.create(
            producto=self.producto,
            bodega=self.bodega,
            tipo='salida',
            cantidad=5
        )

        self.producto.refresh_from_db()
        self.assertEqual(self.producto.stock_actual, 15)


class APITestCase(APITestCase):
    def setUp(self):
        self.categoria = Categoria.objects.create(
            nombre="Electrónicos",
            descripcion="Productos electrónicos"
        )
        self.proveedor = Proveedor.objects.create(
            razon_social="TechSupply S.A.",
            rut="12345678-9",
            email="<EMAIL>",
            telefono="+56912345678"
        )

    def test_crear_producto_via_api(self):
        url = '/api/productos/'
        data = {
            'sku': 'API001',
            'nombre': 'Producto API',
            'categoria': self.categoria.id,
            'proveedor': self.proveedor.id,
            'precio': 15000.00
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Producto.objects.count(), 1)

    def test_listar_productos(self):
        Producto.objects.create(
            sku="LIST001",
            nombre="Producto Lista",
            categoria=self.categoria,
            proveedor=self.proveedor,
            precio=5000.00
        )

        url = '/api/productos/'
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

    def test_productos_bajo_stock(self):
        # Crear producto con stock bajo
        producto = Producto.objects.create(
            sku="BAJO001",
            nombre="Producto Bajo Stock",
            categoria=self.categoria,
            proveedor=self.proveedor,
            precio=5000.00,
            stock_actual=5  # Menos de 10
        )

        url = '/api/productos/bajo_stock/'
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['sku'], 'BAJO001')
