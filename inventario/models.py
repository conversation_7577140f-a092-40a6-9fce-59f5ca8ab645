from django.db import models
from django.core.exceptions import ValidationError


class Categoria(models.Model):
    nombre = models.CharField(max_length=100, unique=True)
    descripcion = models.TextField(blank=True)

    def __str__(self):
        return self.nombre

    class Meta:
        verbose_name_plural = "Categorías"


class Proveedor(models.Model):
    razon_social = models.CharField(max_length=200)
    rut = models.CharField(max_length=12, unique=True)
    email = models.EmailField()
    telefono = models.CharField(max_length=15)

    def __str__(self):
        return self.razon_social

    class Meta:
        verbose_name_plural = "Proveedores"


class Bodega(models.Model):
    nombre = models.Char<PERSON>ield(max_length=100)
    ubicacion = models.CharField(max_length=200)

    def __str__(self):
        return self.nombre


class Producto(models.Model):
    sku = models.CharField(max_length=50, unique=True)
    nombre = models.Char<PERSON>ield(max_length=200)
    categoria = models.ForeignKey(Categoria, on_delete=models.CASCADE)
    proveedor = models.ForeignKey(Proveedor, on_delete=models.CASCADE)
    precio = models.DecimalField(max_digits=10, decimal_places=2)
    stock_actual = models.IntegerField(default=0)

    def __str__(self):
        return f"{self.sku} - {self.nombre}"


class Movimiento(models.Model):
    TIPO_CHOICES = [
        ('entrada', 'Entrada'),
        ('salida', 'Salida'),
        ('merma', 'Merma'),
    ]

    producto = models.ForeignKey(Producto, on_delete=models.CASCADE)
    bodega = models.ForeignKey(Bodega, on_delete=models.CASCADE)
    tipo = models.CharField(max_length=10, choices=TIPO_CHOICES)
    cantidad = models.IntegerField()
    fecha = models.DateTimeField(auto_now_add=True)
    observacion = models.TextField(blank=True)

    def clean(self):
        if self.tipo in ['salida', 'merma'] and self.cantidad > self.producto.stock_actual:
            raise ValidationError(f'Stock insuficiente. Stock actual: {self.producto.stock_actual}')

    def save(self, *args, **kwargs):
        self.full_clean()

        # Actualizar stock del producto
        if self.tipo == 'entrada':
            self.producto.stock_actual += self.cantidad
        elif self.tipo in ['salida', 'merma']:
            self.producto.stock_actual -= self.cantidad

        self.producto.save()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.tipo.title()} - {self.producto.nombre} - {self.cantidad}"
