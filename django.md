graph TD
    A[Inicio] --> B{Configurar admin.py};
    B --> C[Importar modelos];
    C --> D[Registrar modelos en admin.site.register];
    D --> E{Personalizar modelos};
    E --> F[Crear clase ModelAdmin];
    F --> G[Agregar list_display];
    F --> H[Agregar search_fields];
    F --> I[Agregar list_filter];
    I --> J[Pasar ModelAdmin a admin.site.register];
    J --> K[Ejecutar runserver];
    K --> L[Acceder a /admin];
    L --> M[Verificar personalización];
    M --> N[Fin];
