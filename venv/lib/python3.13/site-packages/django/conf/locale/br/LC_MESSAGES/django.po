# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2012,2014
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-21 10:22+0200\n"
"PO-Revision-Date: 2021-11-18 21:19+0000\n"
"Last-Translator: Transifex Bot <>\n"
"Language-Team: Breton (http://www.transifex.com/django/django/language/br/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: br\n"
"Plural-Forms: nplurals=5; plural=((n%10 == 1) && (n%100 != 11) && (n%100 !"
"=71) && (n%100 !=91) ? 0 :(n%10 == 2) && (n%100 != 12) && (n%100 !=72) && (n"
"%100 !=92) ? 1 :(n%10 ==3 || n%10==4 || n%10==9) && (n%100 < 10 || n% 100 > "
"19) && (n%100 < 70 || n%100 > 79) && (n%100 < 90 || n%100 > 99) ? 2 :(n != 0 "
"&& n % 1000000 == 0) ? 3 : 4);\n"

msgid "Afrikaans"
msgstr "Afrikaneg"

msgid "Arabic"
msgstr "Arabeg"

msgid "Algerian Arabic"
msgstr ""

msgid "Asturian"
msgstr "Astureg"

msgid "Azerbaijani"
msgstr "Azeri"

msgid "Bulgarian"
msgstr "Bulgareg"

msgid "Belarusian"
msgstr "Belaruseg"

msgid "Bengali"
msgstr "Bengaleg"

msgid "Breton"
msgstr "Brezhoneg"

msgid "Bosnian"
msgstr "Bosneg"

msgid "Catalan"
msgstr "Katalaneg"

msgid "Czech"
msgstr "Tchekeg"

msgid "Welsh"
msgstr "Kembraeg"

msgid "Danish"
msgstr "Daneg"

msgid "German"
msgstr "Alamaneg"

msgid "Lower Sorbian"
msgstr ""

msgid "Greek"
msgstr "Gresianeg"

msgid "English"
msgstr "Saozneg"

msgid "Australian English"
msgstr "Saozneg Aostralia"

msgid "British English"
msgstr "Saozneg Breizh-Veur"

msgid "Esperanto"
msgstr "Esperanteg"

msgid "Spanish"
msgstr "Spagnoleg"

msgid "Argentinian Spanish"
msgstr "Spagnoleg Arc'hantina"

msgid "Colombian Spanish"
msgstr "Spagnoleg Kolombia"

msgid "Mexican Spanish"
msgstr "Spagnoleg Mec'hiko"

msgid "Nicaraguan Spanish"
msgstr "Spagnoleg Nicaragua"

msgid "Venezuelan Spanish"
msgstr "Spagnoleg Venezuela"

msgid "Estonian"
msgstr "Estoneg"

msgid "Basque"
msgstr "Euskareg"

msgid "Persian"
msgstr "Perseg"

msgid "Finnish"
msgstr "Finneg"

msgid "French"
msgstr "Galleg"

msgid "Frisian"
msgstr "Frizeg"

msgid "Irish"
msgstr "Iwerzhoneg"

msgid "Scottish Gaelic"
msgstr ""

msgid "Galician"
msgstr "Galizeg"

msgid "Hebrew"
msgstr "Hebraeg"

msgid "Hindi"
msgstr "Hindi"

msgid "Croatian"
msgstr "Kroateg"

msgid "Upper Sorbian"
msgstr ""

msgid "Hungarian"
msgstr "Hungareg"

msgid "Armenian"
msgstr ""

msgid "Interlingua"
msgstr "Interlingua"

msgid "Indonesian"
msgstr "Indonezeg"

msgid "Igbo"
msgstr ""

msgid "Ido"
msgstr "Ido"

msgid "Icelandic"
msgstr "Islandeg"

msgid "Italian"
msgstr "Italianeg"

msgid "Japanese"
msgstr "Japaneg"

msgid "Georgian"
msgstr "Jorjianeg"

msgid "Kabyle"
msgstr ""

msgid "Kazakh"
msgstr "kazak"

msgid "Khmer"
msgstr "Khmer"

msgid "Kannada"
msgstr "Kannata"

msgid "Korean"
msgstr "Koreaneg"

msgid "Kyrgyz"
msgstr ""

msgid "Luxembourgish"
msgstr "Luksembourgeg"

msgid "Lithuanian"
msgstr "Lituaneg"

msgid "Latvian"
msgstr "Latveg"

msgid "Macedonian"
msgstr "Makedoneg"

msgid "Malayalam"
msgstr "Malayalam"

msgid "Mongolian"
msgstr "Mongoleg"

msgid "Marathi"
msgstr "Marathi"

msgid "Malay"
msgstr ""

msgid "Burmese"
msgstr "Burmeg"

msgid "Norwegian Bokmål"
msgstr ""

msgid "Nepali"
msgstr "nepaleg"

msgid "Dutch"
msgstr "Nederlandeg"

msgid "Norwegian Nynorsk"
msgstr "Norvegeg Nynorsk"

msgid "Ossetic"
msgstr "Oseteg"

msgid "Punjabi"
msgstr "Punjabeg"

msgid "Polish"
msgstr "Poloneg"

msgid "Portuguese"
msgstr "Portugaleg"

msgid "Brazilian Portuguese"
msgstr "Portugaleg Brazil"

msgid "Romanian"
msgstr "Roumaneg"

msgid "Russian"
msgstr "Rusianeg"

msgid "Slovak"
msgstr "Slovakeg"

msgid "Slovenian"
msgstr "Sloveneg"

msgid "Albanian"
msgstr "Albaneg"

msgid "Serbian"
msgstr "Serbeg"

msgid "Serbian Latin"
msgstr "Serbeg e lizherennoù latin"

msgid "Swedish"
msgstr "Svedeg"

msgid "Swahili"
msgstr "swahileg"

msgid "Tamil"
msgstr "Tamileg"

msgid "Telugu"
msgstr "Telougou"

msgid "Tajik"
msgstr ""

msgid "Thai"
msgstr "Thai"

msgid "Turkmen"
msgstr ""

msgid "Turkish"
msgstr "Turkeg"

msgid "Tatar"
msgstr "tatar"

msgid "Udmurt"
msgstr "Oudmourteg"

msgid "Ukrainian"
msgstr "Ukraineg"

msgid "Urdu"
msgstr "Ourdou"

msgid "Uzbek"
msgstr ""

msgid "Vietnamese"
msgstr "Vietnameg"

msgid "Simplified Chinese"
msgstr "Sinaeg eeunaet"

msgid "Traditional Chinese"
msgstr "Sinaeg hengounel"

msgid "Messages"
msgstr "Kemennadenn"

msgid "Site Maps"
msgstr "Tresoù al lec'hienn"

msgid "Static Files"
msgstr "Restroù statek"

msgid "Syndication"
msgstr "Sindikadur"

#. Translators: String used to replace omitted page numbers in elided page
#. range generated by paginators, e.g. [1, 2, '…', 5, 6, 7, '…', 9, 10].
msgid "…"
msgstr "..."

msgid "That page number is not an integer"
msgstr ""

msgid "That page number is less than 1"
msgstr "An niver a bajenn mañ a zo bihanoc'h eget 1."

msgid "That page contains no results"
msgstr "N'eus disoc'h er pajenn-mañ."

msgid "Enter a valid value."
msgstr "Merkit un talvoud reizh"

msgid "Enter a valid URL."
msgstr "Merkit un URL reizh"

msgid "Enter a valid integer."
msgstr "Merkit un niver anterin reizh."

msgid "Enter a valid email address."
msgstr "Merkit ur chomlec'h postel reizh"

#. Translators: "letters" means latin letters: a-z and A-Z.
msgid ""
"Enter a valid “slug” consisting of letters, numbers, underscores or hyphens."
msgstr ""

msgid ""
"Enter a valid “slug” consisting of Unicode letters, numbers, underscores, or "
"hyphens."
msgstr ""

msgid "Enter a valid IPv4 address."
msgstr "Merkit ur chomlec'h IPv4 reizh."

msgid "Enter a valid IPv6 address."
msgstr "Merkit ur chomlec'h IPv6 reizh."

msgid "Enter a valid IPv4 or IPv6 address."
msgstr "Merkit ur chomlec'h IPv4 pe IPv6 reizh."

msgid "Enter only digits separated by commas."
msgstr "Merkañ hepken sifroù dispartiet dre skejoù."

#, python-format
msgid "Ensure this value is %(limit_value)s (it is %(show_value)s)."
msgstr ""
"Bezit sur ez eo an talvoud-mañ %(limit_value)s (evit ar mare ez eo "
"%(show_value)s)."

#, python-format
msgid "Ensure this value is less than or equal to %(limit_value)s."
msgstr "Gwiriit mat emañ an talvoud-mañ a-is pe par da %(limit_value)s."

#, python-format
msgid "Ensure this value is greater than or equal to %(limit_value)s."
msgstr "Gwiriit mat emañ an talvoud-mañ a-us pe par da %(limit_value)s."

#, python-format
msgid ""
"Ensure this value has at least %(limit_value)d character (it has "
"%(show_value)d)."
msgid_plural ""
"Ensure this value has at least %(limit_value)d characters (it has "
"%(show_value)d)."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""
msgstr[4] ""

#, python-format
msgid ""
"Ensure this value has at most %(limit_value)d character (it has "
"%(show_value)d)."
msgid_plural ""
"Ensure this value has at most %(limit_value)d characters (it has "
"%(show_value)d)."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""
msgstr[4] ""

msgid "Enter a number."
msgstr "Merkit un niver."

#, python-format
msgid "Ensure that there are no more than %(max)s digit in total."
msgid_plural "Ensure that there are no more than %(max)s digits in total."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""
msgstr[4] ""

#, python-format
msgid "Ensure that there are no more than %(max)s decimal place."
msgid_plural "Ensure that there are no more than %(max)s decimal places."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""
msgstr[4] ""

#, python-format
msgid ""
"Ensure that there are no more than %(max)s digit before the decimal point."
msgid_plural ""
"Ensure that there are no more than %(max)s digits before the decimal point."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""
msgstr[4] ""

#, python-format
msgid ""
"File extension “%(extension)s” is not allowed. Allowed extensions are: "
"%(allowed_extensions)s."
msgstr ""

msgid "Null characters are not allowed."
msgstr ""

msgid "and"
msgstr "ha"

#, python-format
msgid "%(model_name)s with this %(field_labels)s already exists."
msgstr ""

#, python-format
msgid "Value %(value)r is not a valid choice."
msgstr ""

msgid "This field cannot be null."
msgstr "N'hall ket ar vaezienn chom goullo"

msgid "This field cannot be blank."
msgstr "N'hall ket ar vaezienn chom goullo"

#, python-format
msgid "%(model_name)s with this %(field_label)s already exists."
msgstr "Bez' ez eus c'hoazh eus ur %(model_name)s gant ar %(field_label)s-mañ."

#. Translators: The 'lookup_type' is one of 'date', 'year' or 'month'.
#. Eg: "Title must be unique for pub_date year"
#, python-format
msgid ""
"%(field_label)s must be unique for %(date_field_label)s %(lookup_type)s."
msgstr ""

#, python-format
msgid "Field of type: %(field_type)s"
msgstr "Seurt maezienn : %(field_type)s"

#, python-format
msgid "“%(value)s” value must be either True or False."
msgstr ""

#, python-format
msgid "“%(value)s” value must be either True, False, or None."
msgstr ""

msgid "Boolean (Either True or False)"
msgstr "Boulean (gwir pe gaou)"

#, python-format
msgid "String (up to %(max_length)s)"
msgstr "neudennad arouezennoù (betek %(max_length)s)"

msgid "Comma-separated integers"
msgstr "Niveroù anterin dispartiet dre ur skej"

#, python-format
msgid ""
"“%(value)s” value has an invalid date format. It must be in YYYY-MM-DD "
"format."
msgstr ""

#, python-format
msgid ""
"“%(value)s” value has the correct format (YYYY-MM-DD) but it is an invalid "
"date."
msgstr ""

msgid "Date (without time)"
msgstr "Deizad (hep eur)"

#, python-format
msgid ""
"“%(value)s” value has an invalid format. It must be in YYYY-MM-DD HH:MM[:ss[."
"uuuuuu]][TZ] format."
msgstr ""

#, python-format
msgid ""
"“%(value)s” value has the correct format (YYYY-MM-DD HH:MM[:ss[.uuuuuu]]"
"[TZ]) but it is an invalid date/time."
msgstr ""

msgid "Date (with time)"
msgstr "Deizad (gant an eur)"

#, python-format
msgid "“%(value)s” value must be a decimal number."
msgstr ""

msgid "Decimal number"
msgstr "Niver dekvedennel"

#, python-format
msgid ""
"“%(value)s” value has an invalid format. It must be in [DD] [[HH:]MM:]ss[."
"uuuuuu] format."
msgstr ""

msgid "Duration"
msgstr ""

msgid "Email address"
msgstr "Chomlec'h postel"

msgid "File path"
msgstr "Treug war-du ar restr"

#, python-format
msgid "“%(value)s” value must be a float."
msgstr ""

msgid "Floating point number"
msgstr "Niver gant skej nij"

#, python-format
msgid "“%(value)s” value must be an integer."
msgstr ""

msgid "Integer"
msgstr "Anterin"

msgid "Big (8 byte) integer"
msgstr "Anterin bras (8 okted)"

msgid "Small integer"
msgstr "Niver anterin bihan"

msgid "IPv4 address"
msgstr "Chomlec'h IPv4"

msgid "IP address"
msgstr "Chomlec'h IP"

#, python-format
msgid "“%(value)s” value must be either None, True or False."
msgstr ""

msgid "Boolean (Either True, False or None)"
msgstr "Boulean (gwir pe gaou pe netra)"

msgid "Positive big integer"
msgstr ""

msgid "Positive integer"
msgstr "Niver anterin pozitivel"

msgid "Positive small integer"
msgstr "Niver anterin bihan pozitivel"

#, python-format
msgid "Slug (up to %(max_length)s)"
msgstr "Slug (betek %(max_length)s arouez.)"

msgid "Text"
msgstr "Testenn"

#, python-format
msgid ""
"“%(value)s” value has an invalid format. It must be in HH:MM[:ss[.uuuuuu]] "
"format."
msgstr ""

#, python-format
msgid ""
"“%(value)s” value has the correct format (HH:MM[:ss[.uuuuuu]]) but it is an "
"invalid time."
msgstr ""

msgid "Time"
msgstr "Eur"

msgid "URL"
msgstr "URL"

msgid "Raw binary data"
msgstr ""

#, python-format
msgid "“%(value)s” is not a valid UUID."
msgstr ""

msgid "Universally unique identifier"
msgstr ""

msgid "File"
msgstr "Restr"

msgid "Image"
msgstr "Skeudenn"

msgid "A JSON object"
msgstr ""

msgid "Value must be valid JSON."
msgstr ""

#, python-format
msgid "%(model)s instance with %(field)s %(value)r does not exist."
msgstr ""

msgid "Foreign Key (type determined by related field)"
msgstr "Alc'hwez estren (seurt termenet dre ar vaezienn liammet)"

msgid "One-to-one relationship"
msgstr "Darempred unan-ouzh-unan"

#, python-format
msgid "%(from)s-%(to)s relationship"
msgstr ""

#, python-format
msgid "%(from)s-%(to)s relationships"
msgstr ""

msgid "Many-to-many relationship"
msgstr "Darempred lies-ouzh-lies"

#. Translators: If found as last label character, these punctuation
#. characters will prevent the default label_suffix to be appended to the
#. label
msgid ":?.!"
msgstr ""

msgid "This field is required."
msgstr "Rekis eo leuniañ ar vaezienn."

msgid "Enter a whole number."
msgstr "Merkit un niver anterin."

msgid "Enter a valid date."
msgstr "Merkit un deiziad reizh"

msgid "Enter a valid time."
msgstr "Merkit un eur reizh"

msgid "Enter a valid date/time."
msgstr "Merkit un eur/deiziad reizh"

msgid "Enter a valid duration."
msgstr ""

#, python-brace-format
msgid "The number of days must be between {min_days} and {max_days}."
msgstr ""

msgid "No file was submitted. Check the encoding type on the form."
msgstr "N'eus ket kaset restr ebet. Gwiriit ar seurt enkodañ evit ar restr"

msgid "No file was submitted."
msgstr "N'eus bet kaset restr ebet."

msgid "The submitted file is empty."
msgstr "Goullo eo ar restr kaset."

#, python-format
msgid "Ensure this filename has at most %(max)d character (it has %(length)d)."
msgid_plural ""
"Ensure this filename has at most %(max)d characters (it has %(length)d)."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""
msgstr[4] ""

msgid "Please either submit a file or check the clear checkbox, not both."
msgstr "Kasit ur restr pe askit al log riñsañ; an eil pe egile"

msgid ""
"Upload a valid image. The file you uploaded was either not an image or a "
"corrupted image."
msgstr ""
"Enpozhiit ur skeudenn reizh. Ar seurt bet enporzhiet ganeoc'h a oa foeltret "
"pe ne oa ket ur skeudenn"

#, python-format
msgid "Select a valid choice. %(value)s is not one of the available choices."
msgstr "Dizuit un dibab reizh. %(value)s n'emañ ket e-touez an dibaboù posupl."

msgid "Enter a list of values."
msgstr "Merkit ur roll talvoudoù"

msgid "Enter a complete value."
msgstr "Merkañ un talvoud klok"

msgid "Enter a valid UUID."
msgstr ""

msgid "Enter a valid JSON."
msgstr ""

#. Translators: This is the default suffix added to form field labels
msgid ":"
msgstr ""

#, python-format
msgid "(Hidden field %(name)s) %(error)s"
msgstr ""

#, python-format
msgid ""
"ManagementForm data is missing or has been tampered with. Missing fields: "
"%(field_names)s. You may need to file a bug report if the issue persists."
msgstr ""

#, python-format
msgid "Please submit at most %d form."
msgid_plural "Please submit at most %d forms."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""
msgstr[4] ""

#, python-format
msgid "Please submit at least %d form."
msgid_plural "Please submit at least %d forms."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""
msgstr[4] ""

msgid "Order"
msgstr "Urzh"

msgid "Delete"
msgstr "Diverkañ"

#, python-format
msgid "Please correct the duplicate data for %(field)s."
msgstr "Reizhit ar roadennoù e doubl e %(field)s."

#, python-format
msgid "Please correct the duplicate data for %(field)s, which must be unique."
msgstr ""
"Reizhit ar roadennoù e doubl e %(field)s, na zle bezañ enni nemet talvoudoù "
"dzho o-unan."

#, python-format
msgid ""
"Please correct the duplicate data for %(field_name)s which must be unique "
"for the %(lookup)s in %(date_field)s."
msgstr ""
"Reizhit ar roadennoù e doubl e %(field_name)s a rank bezañ ennañ talvodoù en "
"o-unan evit lodenn %(lookup)s %(date_field)s."

msgid "Please correct the duplicate values below."
msgstr "Reizhañ ar roadennoù e doubl zo a-is"

msgid "The inline value did not match the parent instance."
msgstr ""

msgid "Select a valid choice. That choice is not one of the available choices."
msgstr "Diuzit un dibab reizh. N'emañ ket an dibab-mañ e-touez ar re bosupl."

#, python-format
msgid "“%(pk)s” is not a valid value."
msgstr ""

#, python-format
msgid ""
"%(datetime)s couldn’t be interpreted in time zone %(current_timezone)s; it "
"may be ambiguous or it may not exist."
msgstr ""

msgid "Clear"
msgstr "Riñsañ"

msgid "Currently"
msgstr "Evit ar mare"

msgid "Change"
msgstr "Kemmañ"

msgid "Unknown"
msgstr "Dianav"

msgid "Yes"
msgstr "Ya"

msgid "No"
msgstr "Ket"

#. Translators: Please do not add spaces around commas.
msgid "yes,no,maybe"
msgstr "ya,ket,marteze"

#, python-format
msgid "%(size)d byte"
msgid_plural "%(size)d bytes"
msgstr[0] "%(size)d okted"
msgstr[1] "%(size)d okted"
msgstr[2] "%(size)d okted"
msgstr[3] "%(size)d okted"
msgstr[4] "%(size)d okted"

#, python-format
msgid "%s KB"
msgstr "%s KB"

#, python-format
msgid "%s MB"
msgstr "%s MB"

#, python-format
msgid "%s GB"
msgstr "%s GB"

#, python-format
msgid "%s TB"
msgstr "%s TB"

#, python-format
msgid "%s PB"
msgstr "%s PB"

msgid "p.m."
msgstr "g.m."

msgid "a.m."
msgstr "mintin"

msgid "PM"
msgstr "G.M."

msgid "AM"
msgstr "Mintin"

msgid "midnight"
msgstr "hanternoz"

msgid "noon"
msgstr "kreisteiz"

msgid "Monday"
msgstr "Lun"

msgid "Tuesday"
msgstr "Meurzh"

msgid "Wednesday"
msgstr "Merc'her"

msgid "Thursday"
msgstr "Yaou"

msgid "Friday"
msgstr "Gwener"

msgid "Saturday"
msgstr "Sadorn"

msgid "Sunday"
msgstr "Sul"

msgid "Mon"
msgstr "Lun"

msgid "Tue"
msgstr "Meu"

msgid "Wed"
msgstr "Mer"

msgid "Thu"
msgstr "Yao"

msgid "Fri"
msgstr "Gwe"

msgid "Sat"
msgstr "Sad"

msgid "Sun"
msgstr "Sul"

msgid "January"
msgstr "Genver"

msgid "February"
msgstr "C'hwevrer"

msgid "March"
msgstr "Meurzh"

msgid "April"
msgstr "Ebrel"

msgid "May"
msgstr "Mae"

msgid "June"
msgstr "Mezheven"

msgid "July"
msgstr "Gouere"

msgid "August"
msgstr "Eost"

msgid "September"
msgstr "Gwengolo"

msgid "October"
msgstr "Here"

msgid "November"
msgstr "Du"

msgid "December"
msgstr "Kerzu"

msgid "jan"
msgstr "Gen"

msgid "feb"
msgstr "C'hwe"

msgid "mar"
msgstr "Meu"

msgid "apr"
msgstr "Ebr"

msgid "may"
msgstr "Mae"

msgid "jun"
msgstr "Mez"

msgid "jul"
msgstr "Gou"

msgid "aug"
msgstr "Eos"

msgid "sep"
msgstr "Gwe"

msgid "oct"
msgstr "Her"

msgid "nov"
msgstr "Du"

msgid "dec"
msgstr "Kzu"

msgctxt "abbrev. month"
msgid "Jan."
msgstr "Gen."

msgctxt "abbrev. month"
msgid "Feb."
msgstr "C'hwe."

msgctxt "abbrev. month"
msgid "March"
msgstr "Meu."

msgctxt "abbrev. month"
msgid "April"
msgstr "Ebr."

msgctxt "abbrev. month"
msgid "May"
msgstr "Mae"

msgctxt "abbrev. month"
msgid "June"
msgstr "Mez."

msgctxt "abbrev. month"
msgid "July"
msgstr "Gou."

msgctxt "abbrev. month"
msgid "Aug."
msgstr "Eos."

msgctxt "abbrev. month"
msgid "Sept."
msgstr "Gwe."

msgctxt "abbrev. month"
msgid "Oct."
msgstr "Her."

msgctxt "abbrev. month"
msgid "Nov."
msgstr "Du"

msgctxt "abbrev. month"
msgid "Dec."
msgstr "Kzu"

msgctxt "alt. month"
msgid "January"
msgstr "Genver"

msgctxt "alt. month"
msgid "February"
msgstr "C'hwevrer"

msgctxt "alt. month"
msgid "March"
msgstr "Meurzh"

msgctxt "alt. month"
msgid "April"
msgstr "Ebrel"

msgctxt "alt. month"
msgid "May"
msgstr "Mae"

msgctxt "alt. month"
msgid "June"
msgstr "Mezheven"

msgctxt "alt. month"
msgid "July"
msgstr "Gouere"

msgctxt "alt. month"
msgid "August"
msgstr "Eost"

msgctxt "alt. month"
msgid "September"
msgstr "Gwengolo"

msgctxt "alt. month"
msgid "October"
msgstr "Here"

msgctxt "alt. month"
msgid "November"
msgstr "Du"

msgctxt "alt. month"
msgid "December"
msgstr "Kerzu"

msgid "This is not a valid IPv6 address."
msgstr ""

#, python-format
msgctxt "String to return when truncating text"
msgid "%(truncated_text)s…"
msgstr ""

msgid "or"
msgstr "pe"

#. Translators: This string is used as a separator between list elements
msgid ", "
msgstr ","

#, python-format
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""
msgstr[4] ""

#, python-format
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""
msgstr[4] ""

#, python-format
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""
msgstr[4] ""

#, python-format
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""
msgstr[4] ""

#, python-format
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""
msgstr[4] ""

#, python-format
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""
msgstr[4] ""

msgid "Forbidden"
msgstr "Difennet"

msgid "CSRF verification failed. Request aborted."
msgstr ""

msgid ""
"You are seeing this message because this HTTPS site requires a “Referer "
"header” to be sent by your web browser, but none was sent. This header is "
"required for security reasons, to ensure that your browser is not being "
"hijacked by third parties."
msgstr ""

msgid ""
"If you have configured your browser to disable “Referer” headers, please re-"
"enable them, at least for this site, or for HTTPS connections, or for “same-"
"origin” requests."
msgstr ""

msgid ""
"If you are using the <meta name=\"referrer\" content=\"no-referrer\"> tag or "
"including the “Referrer-Policy: no-referrer” header, please remove them. The "
"CSRF protection requires the “Referer” header to do strict referer checking. "
"If you’re concerned about privacy, use alternatives like <a rel=\"noreferrer"
"\" …> for links to third-party sites."
msgstr ""

msgid ""
"You are seeing this message because this site requires a CSRF cookie when "
"submitting forms. This cookie is required for security reasons, to ensure "
"that your browser is not being hijacked by third parties."
msgstr ""

msgid ""
"If you have configured your browser to disable cookies, please re-enable "
"them, at least for this site, or for “same-origin” requests."
msgstr ""

msgid "More information is available with DEBUG=True."
msgstr ""

msgid "No year specified"
msgstr "N'eus bet resisaet bloavezh ebet"

msgid "Date out of range"
msgstr ""

msgid "No month specified"
msgstr "N'eus bet resisaet miz ebet"

msgid "No day specified"
msgstr "N'eus bet resisaet deiz ebet"

msgid "No week specified"
msgstr "N'eus bet resisaet sizhun ebet"

#, python-format
msgid "No %(verbose_name_plural)s available"
msgstr "N'eus %(verbose_name_plural)s ebet da gaout."

#, python-format
msgid ""
"Future %(verbose_name_plural)s not available because %(class_name)s."
"allow_future is False."
msgstr ""
"En dazont ne vo ket a %(verbose_name_plural)s rak faos eo %(class_name)s."
"allow_future."

#, python-format
msgid "Invalid date string “%(datestr)s” given format “%(format)s”"
msgstr ""

#, python-format
msgid "No %(verbose_name)s found matching the query"
msgstr ""
"N'eus bet kavet traezenn %(verbose_name)s ebet o klotaén gant ar goulenn"

msgid "Page is not “last”, nor can it be converted to an int."
msgstr ""

#, python-format
msgid "Invalid page (%(page_number)s): %(message)s"
msgstr ""

#, python-format
msgid "Empty list and “%(class_name)s.allow_empty” is False."
msgstr ""

msgid "Directory indexes are not allowed here."
msgstr "N'haller ket diskwel endalc'had ar c'havlec'h-mañ."

#, python-format
msgid "“%(path)s” does not exist"
msgstr ""

#, python-format
msgid "Index of %(directory)s"
msgstr "Meneger %(directory)s"

msgid "The install worked successfully! Congratulations!"
msgstr ""

#, python-format
msgid ""
"View <a href=\"https://docs.djangoproject.com/en/%(version)s/releases/\" "
"target=\"_blank\" rel=\"noopener\">release notes</a> for Django %(version)s"
msgstr ""

#, python-format
msgid ""
"You are seeing this page because <a href=\"https://docs.djangoproject.com/en/"
"%(version)s/ref/settings/#debug\" target=\"_blank\" rel=\"noopener"
"\">DEBUG=True</a> is in your settings file and you have not configured any "
"URLs."
msgstr ""

msgid "Django Documentation"
msgstr ""

msgid "Topics, references, &amp; how-to’s"
msgstr ""

msgid "Tutorial: A Polling App"
msgstr ""

msgid "Get started with Django"
msgstr ""

msgid "Django Community"
msgstr ""

msgid "Connect, get help, or contribute"
msgstr ""
