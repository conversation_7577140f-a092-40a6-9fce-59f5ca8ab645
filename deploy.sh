#!/bin/bash

# Script de despliegue para el sistema de inventario

echo "🚀 Iniciando despliegue del Sistema de Inventario..."

# Instalar dependencias
echo "📦 Instalando dependencias..."
pip install -r requirements.txt

# Configurar variables de entorno
if [ ! -f .env ]; then
    echo "⚠️  Archivo .env no encontrado. Copiando desde .env.example..."
    cp .env.example .env
    echo "✏️  Por favor, edita el archivo .env con tus configuraciones."
fi

# Ejecutar migraciones
echo "🗄️  Ejecutando migraciones..."
python manage.py makemigrations
python manage.py migrate

# Crear superusuario si no existe
echo "👤 Verificando superusuario..."
python manage.py shell -c "
from django.contrib.auth.models import User
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print('Superusuario creado: admin/admin123')
else:
    print('Superusuario ya existe')
"

# Cargar datos de prueba
echo "📊 Cargando datos de prueba..."
python manage.py cargar_datos

# Recopilar archivos estáticos (para producción)
if [ "$1" = "production" ]; then
    echo "📁 Recopilando archivos estáticos..."
    python manage.py collectstatic --noinput
fi

echo "✅ Despliegue completado!"
echo ""
echo "🌐 Para iniciar el servidor:"
echo "   python manage.py runserver"
echo ""
echo "🔧 Admin panel: http://localhost:8000/admin/"
echo "   Usuario: admin"
echo "   Contraseña: admin123"
echo ""
echo "📡 API endpoints: http://localhost:8000/api/"
