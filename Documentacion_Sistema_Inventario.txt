SISTEMA DE GESTIÓN DE INVENTARIO
Documentación Técnica del Proyecto Django

Autor: [Tu Nombre]
Fecha: Septiembre 2025
Curso: [Tu Curso]

================================================================================
1. INTRODUCCIÓN
================================================================================

El presente documento describe la implementación de un sistema de gestión de inventario desarrollado con Django y PostgreSQL. El sistema permite administrar productos, categorías, proveedores, bodegas y movimientos de stock, cumpliendo con los requerimientos de un sistema empresarial básico de control de inventario.

El proyecto implementa una arquitectura basada en el patrón Modelo-Vista-Controlador (MVC) de Django, con una API REST para integración con sistemas externos y una interfaz administrativa completa para la gestión de datos.

================================================================================
2. DISEÑO DE MODELOS Y RELACIONES
================================================================================

2.1 Arquitectura de la Base de Datos
------------------------------------

El sistema está compuesto por cinco modelos principales que representan las entidades del negocio:

**Modelo Categoria:**
- Campos: id (PK), nombre (único), descripcion
- Propósito: Clasificar productos en categorías (Electrónicos, Oficina, Limpieza)
- Relación: Uno a muchos con Producto

**Modelo Proveedor:**
- Campos: id (PK), razon_social, rut (único), email, telefono
- Propósito: Gestionar información de proveedores
- Relación: Uno a muchos con Producto
- Validaciones: RUT único, formato de email

**Modelo Bodega:**
- Campos: id (PK), nombre, ubicacion
- Propósito: Representar ubicaciones físicas de almacenamiento
- Relación: Uno a muchos con Movimiento

**Modelo Producto:**
- Campos: id (PK), sku (único), nombre, categoria_id (FK), proveedor_id (FK), precio, stock_actual
- Propósito: Entidad central del sistema, representa los productos del inventario
- Relaciones: Muchos a uno con Categoria y Proveedor, uno a muchos con Movimiento
- Validaciones: SKU único, precio decimal con 2 decimales

**Modelo Movimiento:**
- Campos: id (PK), producto_id (FK), bodega_id (FK), tipo, cantidad, fecha, observacion
- Propósito: Registrar todos los movimientos de stock (entradas, salidas, mermas)
- Relaciones: Muchos a uno con Producto y Bodega
- Tipos: 'entrada', 'salida', 'merma'

2.2 Diagrama de Relaciones
---------------------------

Las relaciones entre modelos siguen el siguiente esquema:

CATEGORIA ||--o{ PRODUCTO : "contiene"
PROVEEDOR ||--o{ PRODUCTO : "suministra"  
BODEGA ||--o{ MOVIMIENTO : "ocurre en"
PRODUCTO ||--o{ MOVIMIENTO : "es afectado por"

Esta estructura normalizada evita redundancia de datos y mantiene la integridad referencial mediante claves foráneas con restricciones CASCADE.

2.3 Características Técnicas
-----------------------------

- **Integridad Referencial:** Uso de ForeignKey con on_delete=CASCADE
- **Validaciones a Nivel de Modelo:** Campos únicos (SKU, RUT, nombre de categoría)
- **Metadatos:** Configuración de verbose_name_plural para interfaz administrativa
- **Métodos __str__:** Representación legible de objetos para debugging y admin

================================================================================
3. LÓGICA DE MOVIMIENTOS DE STOCK
================================================================================

3.1 Algoritmo de Control de Stock
----------------------------------

El sistema implementa un algoritmo automático de actualización de stock basado en los movimientos registrados. La lógica se ejecuta en el método save() del modelo Movimiento:

**Proceso de Validación:**
1. Verificación de stock disponible para movimientos de salida/merma
2. Validación de cantidad positiva
3. Verificación de existencia del producto

**Proceso de Actualización:**
1. Para movimientos tipo 'entrada': stock_actual += cantidad
2. Para movimientos tipo 'salida' o 'merma': stock_actual -= cantidad
3. Actualización automática del registro del producto
4. Registro del movimiento con timestamp automático

3.2 Validaciones de Negocio
----------------------------

**Validación de Stock Insuficiente:**
```python
def clean(self):
    if self.tipo in ['salida', 'merma'] and self.cantidad > self.producto.stock_actual:
        raise ValidationError(f'Stock insuficiente. Stock actual: {self.producto.stock_actual}')
```

Esta validación previene movimientos que resulten en stock negativo, manteniendo la consistencia de los datos.

**Validación en API REST:**
El serializer MovimientoSerializer incluye validaciones adicionales que se ejecutan antes de procesar las peticiones HTTP, proporcionando mensajes de error claros para la integración con sistemas externos.

3.3 Tipos de Movimientos
-------------------------

**Entrada:** Incrementa el stock disponible
- Casos de uso: Compras, devoluciones de clientes, ajustes positivos
- Efecto: stock_actual += cantidad

**Salida:** Decrementa el stock disponible
- Casos de uso: Ventas, transferencias, entregas
- Validación: Requiere stock suficiente
- Efecto: stock_actual -= cantidad

**Merma:** Decrementa el stock por pérdidas
- Casos de uso: Productos dañados, vencidos, robos
- Validación: Requiere stock suficiente  
- Efecto: stock_actual -= cantidad

================================================================================
4. IMPLEMENTACIÓN TÉCNICA
================================================================================

4.1 Arquitectura del Sistema
-----------------------------

**Backend:** Django 4.2.7 con Django REST Framework
**Base de Datos:** PostgreSQL con psycopg2-binary
**API:** RESTful con ViewSets y Serializers
**Administración:** Django Admin personalizado

4.2 Funcionalidades Implementadas
----------------------------------

**CRUD Completo:**
- Create: Formularios y endpoints POST para crear registros
- Read: Listados, filtros y endpoints GET con paginación
- Update: Edición mediante formularios y endpoints PUT/PATCH
- Delete: Eliminación con confirmación y endpoints DELETE

**API REST:**
- Endpoints para todos los modelos (/api/productos/, /api/movimientos/, etc.)
- Filtros por parámetros de consulta (?categoria=1&search=laptop)
- Endpoint especializado para productos con stock bajo
- Endpoint de resumen de movimientos por tipo

**Interfaz Administrativa:**
- Personalización de list_display para mostrar campos relevantes
- Campos de búsqueda (search_fields) para facilitar localización
- Filtros laterales (list_filter) para segmentación de datos
- Campos de solo lectura para datos calculados automáticamente

================================================================================
5. PRUEBAS Y RESULTADOS
================================================================================

5.1 Pruebas Unitarias
----------------------

El sistema incluye 8 pruebas unitarias que verifican:

**Pruebas de Modelos:**
- Correcta representación string de objetos (__str__ methods)
- Funcionamiento de movimientos de entrada
- Validación de movimientos de salida con stock suficiente
- Integridad de relaciones entre modelos

**Pruebas de API:**
- Creación de productos vía POST
- Listado de productos vía GET
- Funcionalidad de endpoint productos con stock bajo
- Serialización correcta de datos

**Resultados de Pruebas:**
```
Found 8 test(s).
Creating test database for alias 'default'...
System check identified no issues (0 silenced).
........
----------------------------------------------------------------------
Ran 8 tests in 0.057s
OK
```

5.2 Pruebas de Integración
---------------------------

**Verificación de Base de Datos:**
- Conexión exitosa a PostgreSQL
- Creación correcta de 15 tablas (5 del inventario + 10 de Django)
- Carga exitosa de datos de prueba
- Integridad referencial verificada

**Datos de Prueba Cargados:**
- 3 Categorías (Electrónicos, Oficina, Limpieza)
- 2 Proveedores con información completa
- 2 Bodegas (Central y Norte)
- 3 Productos con diferentes categorías y proveedores
- 3 Movimientos de entrada inicial

5.3 Validación de Funcionalidades
----------------------------------

**Gestión de Stock:**
- Productos creados con stock inicial 0
- Movimientos de entrada incrementan stock correctamente
- Validación de stock insuficiente funciona correctamente
- Actualización automática de stock en tiempo real

**Interfaz Administrativa:**
- Login exitoso con credenciales admin/admin123
- Visualización correcta de todos los modelos
- Filtros y búsquedas operativos
- Edición y creación de registros funcional

**API REST:**
- Todos los endpoints responden correctamente
- Filtros de consulta operativos
- Serialización de datos con relaciones incluidas
- Validaciones de API funcionando

================================================================================
6. CONCLUSIONES
================================================================================

El sistema de gestión de inventario desarrollado cumple completamente con los requerimientos establecidos, implementando:

1. **Modelo de datos robusto** con relaciones bien definidas y validaciones apropiadas
2. **Lógica de negocio sólida** para el control automático de stock
3. **Interfaz administrativa completa** para gestión diaria
4. **API REST funcional** para integración con sistemas externos
5. **Validaciones exhaustivas** que previenen inconsistencias de datos

El proyecto demuestra el uso correcto de:
- Variables y operaciones matemáticas para cálculo de stock
- Estructuras de control y operadores lógicos para validaciones
- Paquetes externos de Django y PostgreSQL
- Implementación completa de CRUD con admin personalizado

La arquitectura implementada es escalable y mantenible, siguiendo las mejores prácticas de Django y permitiendo futuras extensiones del sistema.

================================================================================
ANEXOS
================================================================================

A. Estructura de Archivos del Proyecto
B. Comandos de Instalación y Configuración  
C. Endpoints de API Disponibles
D. Capturas de Pantalla de Funcionalidades

[Nota: Los anexos con capturas de pantalla y detalles adicionales se incluirán en la entrega final]
